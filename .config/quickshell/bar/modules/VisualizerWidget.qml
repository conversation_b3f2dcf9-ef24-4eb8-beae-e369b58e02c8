import QtQuick
import QtQuick.Layouts
import Quickshell.Io

Rectangle {
    id: visualizerWidget
    width: 150
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    Theme {
        id: theme
    }

    property bool isActive: true
    property bool isSilent: true
    property int silenceCounter: 0
    property var audioData: new Array(20).fill(0)
    property int frameTimeMs: Math.floor(1000 / 60)
    property int updateCounter: 0

    Process {
        id: cavaProcess
        command: ["/sbin/cava", "-p", "/home/<USER>/.config/quickshell/cava_config"]
        running: isActive

        stdout: SplitParser {
            splitMarker: "\n"

            onRead: data => {
                processAudioData(data)
            }
        }

        onExited: {
            console.log("Cava process exited with code:", exitCode)
        }
    }



    Timer {
        id: updateTimer
        interval: frameTimeMs
        running: isActive
        repeat: true
        onTriggered: executeVisualization()
    }

    Row {
        id: visualizerBars
        anchors.centerIn: parent
        spacing: 1

        Repeater {
            id: barRepeater
            model: 20

            Rectangle {
                id: bar
                width: 4
                height: Math.max(2, Math.min(16, getBarHeight(index)))
                color: getBarColor(index)
                radius: 1

                Behavior on height {
                    NumberAnimation {
                        duration: 33
                        easing.type: Easing.OutQuad
                    }
                }

                function getBarHeight(barIndex) {
                    // Force reactivity by using updateCounter
                    updateCounter
                    let level = audioData[barIndex] || 0
                    return level * 2 + 2
                }

                function getBarColor(barIndex) {
                    if (isSilent && silenceCounter > 60) {
                        return Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2)
                    }

                    let level = audioData[barIndex] || 0
                    let intensity = Math.min(1.0, level / 7.0)
                    let alpha = 0.3 + (intensity * 0.7)

                    return Qt.rgba(
                        theme.accent.r,
                        theme.accent.g,
                        theme.accent.b,
                        alpha
                    )
                }

                transform: Scale {
                    yScale: -1
                    origin.y: bar.height / 2
                }
            }
        }
    }

    function processAudioData(data) {
        if (!data || data.length === 0) return

        let line = data.trim()
        if (line.length > 0) {
            let values = line.split(' ')
            let hasAudio = false

            if (values.length >= 20) {
                for (let i = 0; i < Math.min(20, values.length); i++) {
                    let level = parseInt(values[i]) || 0
                    level = Math.min(7, Math.max(0, level))
                    audioData[i] = level

                    if (level > 0) {
                        hasAudio = true
                    }
                }
                // Force UI update by incrementing counter
                updateCounter++
            }

            isSilent = !hasAudio
            if (!isSilent) {
                silenceCounter = 0
            } else {
                silenceCounter++
            }
        }
    }

    function generateMockData() {
        for (let i = 0; i < 20; i++) {
            let baseLevel = Math.sin((Date.now() / 1000 + i * 0.5)) * 3 + 3
            let randomVariation = (Math.random() - 0.5) * 2
            audioData[i] = Math.max(0, Math.min(7, Math.floor(baseLevel + randomVariation)))
        }
        isSilent = false
        silenceCounter = 0
    }

    function generateDemoData() {
        for (let i = 0; i < 20; i++) {
            let wave = Math.sin((Date.now() / 200 + i * 0.3)) * 2 + 2
            audioData[i] = Math.max(0, Math.min(7, Math.floor(wave)))
        }
        isSilent = false
        silenceCounter = 0
    }

    function executeVisualization() {
        if (isSilent && silenceCounter > 120) {
            for (let i = 0; i < audioData.length; i++) {
                audioData[i] = Math.max(0, audioData[i] * 0.9)
            }
        }
    }

    Component.onCompleted: {
        for (let i = 0; i < 20; i++) {
            audioData[i] = 0
        }
    }

    Component.onDestruction: {
        cavaProcess.running = false
    }
}
