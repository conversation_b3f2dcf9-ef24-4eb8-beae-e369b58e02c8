import QtQuick
import QtQuick.Layouts
import Quickshell.Io

Rectangle {
    id: visualizerWidget
    width: 150
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    Theme {
        id: theme
    }

    property bool isActive: true

    Process {
        id: cavaProcess
        command: ["bash", "-c", "cd /home/<USER>/.config/quickshell && cava -p cava_config"]
        running: isActive

        onStdoutChanged: {
            if (stdout) {
                parseAudioData(stdout)
            }
        }
    }

    Text {
        id: cavaOutput
        anchors.centerIn: parent
        text: "▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁"
        color: theme.accent
        font.family: "JetBrains Mono, monospace"
        font.pixelSize: 8
        lineHeight: 0.8
        opacity: 0.7

        transform: Scale {
            xScale: 1
            yScale: -1
            origin.x: cavaOutput.width / 2
            origin.y: cavaOutput.height / 2
        }
    }

    function parseAudioData(data) {
        if (!data) return

        // Cava outputs raw ASCII values (0-8) as characters
        // We need to convert these to bar characters
        let lines = data.split('\n')
        for (let line of lines) {
            if (line.length >= 20) {
                let bars = ""
                let barChars = [" ", "▁", "▂", "▃", "▄", "▅", "▆", "▇", "█"]

                for (let i = 0; i < 20 && i < line.length; i++) {
                    let charCode = line.charCodeAt(i)
                    let level = Math.min(8, Math.max(0, charCode))
                    bars += barChars[level]
                }

                cavaOutput.text = bars
                break
            }
        }
    }

    Component.onCompleted: {
        cavaProcess.running = true
    }
}
