import QtQuick
import QtQuick.Layouts
import Quickshell.Io

Rectangle {
    id: visualizerWidget
    width: 150
    height: 18
    radius: 9
    color: "transparent"
    clip: true

    Theme {
        id: theme
    }

    property bool isActive: true
    property bool isSilent: true
    property int silenceCounter: 0
    property int sleepTimer: 2000
    property var audioData: new Array(20).fill(0)
    property var previousFrame: new Array(20).fill(-1)
    property bool needsRepaint: false
    property int frameTimeMs: Math.floor(1000 / 60)
    property var inputBuffer: []
    property int samplesCounter: 0

    Process {
        id: cavaProcess
        command: ["cava", "-p", "/home/<USER>/.config/quickshell/cava_config"]
        running: isActive

        onStdoutChanged: {
            if (stdout) {
                processRawAudioData(stdout)
            }
        }
    }

    Timer {
        id: updateTimer
        interval: frameTimeMs
        running: isActive
        repeat: true
        onTriggered: executeVisualization()
    }

    Row {
        id: visualizerBars
        anchors.centerIn: parent
        spacing: 1

        Repeater {
            id: barRepeater
            model: 10

            Rectangle {
                id: bar
                width: 4
                height: 2
                color: getBarColor(index)
                radius: 1

                property real targetHeight: Math.max(2, Math.min(16, (audioData[index] || 0) * 2 + 2))

                Behavior on height {
                    NumberAnimation {
                        duration: 33
                        easing.type: Easing.OutQuad
                    }
                }

                function getBarColor(barIndex) {
                    if (isSilent && silenceCounter > 60) {
                        return Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2)
                    }

                    let level = audioData[barIndex] || 0
                    let intensity = Math.min(1.0, level / 7.0)
                    let alpha = 0.3 + (intensity * 0.7)

                    return Qt.rgba(
                        theme.accent.r,
                        theme.accent.g,
                        theme.accent.b,
                        alpha
                    )
                }

                transform: Scale {
                    yScale: -1
                    origin.y: bar.height / 2
                }

                Component.onCompleted: {
                    height = Qt.binding(() => targetHeight)
                }
            }
        }
    }

    function processRawAudioData(rawData) {
        if (!rawData || rawData.length === 0) return

        let lines = rawData.split('\n')
        let hasAudio = false

        for (let line of lines) {
            line = line.trim()
            if (line.length > 0) {
                let values = line.split(' ')
                if (values.length >= 20) {
                    for (let i = 0; i < Math.min(20, values.length); i++) {
                        let level = parseInt(values[i]) || 0
                        level = Math.min(7, Math.max(0, level))
                        audioData[i] = level

                        if (level > 0) {
                            hasAudio = true
                        }
                    }
                    break
                }
            }
        }

        isSilent = !hasAudio
        if (!isSilent) {
            silenceCounter = 0
        } else {
            silenceCounter++
        }
    }

    function executeVisualization() {
        if (isSilent && silenceCounter > 120) {
            for (let i = 0; i < audioData.length; i++) {
                audioData[i] = Math.max(0, audioData[i] * 0.9)
            }
        }

        barRepeater.model = 0
        barRepeater.model = 20
    }

    Component.onCompleted: {
        for (let i = 0; i < 20; i++) {
            audioData[i] = 0
            previousFrame[i] = -1
        }
        cavaProcess.running = true
    }

    Component.onDestruction: {
        cavaProcess.running = false
    }
}
